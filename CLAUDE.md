# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于Python Flask的Outlook邮箱账户管理工具，专注于本地账户管理、邮件计数统计和CSV批量操作功能。项目采用简洁的架构设计，提供直观的Web界面进行账户管理操作。

## 常用开发命令

### 快速启动
```bash
# 安装依赖
pip install -r requirements.txt

# 启动Flask应用
python main.py

# 访问地址
http://localhost:5000
```

### 开发模式
```bash
# 启动开发服务器（自动重载）
export FLASK_ENV=development
python main.py
```

### 打包部署
```bash
# 使用PyInstaller打包
pip install pyinstaller
pyinstaller pyinstaller.spec

# 使用cx_Freeze打包
pip install cx_Freeze
python setup.py build
```

## 核心架构设计

### 技术栈
- **前端框架**: Flask 2.3.3 + Jinja2模板引擎
- **数据存储**: JSON文件 + 内存缓存机制
- **并发控制**: threading.RLock() 文件锁保护
- **用户界面**: 响应式HTML/CSS + Vanilla JavaScript

### 应用架构模式

#### 数据层 (Data Layer)
- **accounts.json**: 存储邮箱账户凭证信息 (refresh_token, client_id)
- **mail_counts.json**: 记录每个邮箱的取件操作次数
- **api_config.json**: API服务器配置信息
- **内存缓存**: `accounts_cache` 提供高性能数据访问

#### 业务逻辑层 (Business Logic)
- **账户管理**: 添加、删除、批量导入账户功能
- **CSV处理**: 支持多种分隔符格式的账户数据导入
- **计数统计**: 邮件取件次数跟踪和统计分析
- **并发安全**: 文件锁和缓存锁确保数据一致性

#### 表现层 (Presentation Layer)
- **路由映射**: Flask路由处理HTTP请求
- **模板渲染**: Jinja2模板生成动态HTML
- **静态资源**: CSS/JS文件服务
- **CORS支持**: 跨域请求处理

### 关键设计模式

#### 缓存同步模式
```python
# 内存缓存与文件持久化的双向同步
accounts_cache = {}  # 内存缓存
def save_cache_to_file():  # 定期同步到文件
    if current_time - last_save_time >= save_interval:
        save_accounts(accounts_cache)
```

#### 原子性写入模式
```python
# 临时文件 + 重命名确保数据完整性
temp_file = ACCOUNTS_FILE + ".tmp"
with open(temp_file, 'w') as f:
    json.dump(accounts, f)
os.rename(temp_file, ACCOUNTS_FILE)
```

#### 并发保护模式
```python
# 双重锁机制保护数据竞争
file_lock = threading.RLock()    # 文件I/O保护
cache_lock = threading.RLock()   # 缓存访问保护
```

### 核心功能模块

#### 1. 账户管理系统 (main.py:358-500)
- **单账户操作**: 添加/删除单个邮箱账户
- **批量操作**: CSV导入、批量删除、批量验证
- **数据验证**: 邮箱格式、token有效性检查
- **错误处理**: 详细的错误日志和用户友好提示

#### 2. CSV导入处理器 (main.py:213-313)
- **多格式支持**: 逗号、制表符、四短横线分隔符
- **智能解析**: 自动检测分隔符类型和标题行
- **错误恢复**: 逐行处理，单行错误不影响整体导入
- **进度反馈**: 详细的导入统计和错误报告

#### 3. 邮件计数系统 (main.py:760-936)
- **计数管理**: 增加、查询、删除邮件操作计数
- **阈值删除**: 根据取件次数批量清理账户
- **统计分析**: 总账户数、活跃账户数、已取件账户数
- **数据关联**: 计数记录与账户信息的同步删除

#### 4. 生命周期管理 (main.py:680-734)
- **浏览器检测**: 心跳机制检测浏览器连接状态
- **优雅关闭**: 程序退出时自动保存缓存数据
- **自动启动**: 程序启动时自动打开浏览器

### 数据存储结构

#### accounts.json 格式
```json
{
  "<EMAIL>": {
    "refresh_token": "token_value_here",
    "client_id": "client_id_value_here"
  }
}
```

#### mail_counts.json 格式
```json
{
  "<EMAIL>": 5,
  "<EMAIL>": 12
}
```

#### api_config.json 格式
```json
{
  "api_base_url": "http://localhost:8000"
}
```

### 前端页面架构

#### 页面路由映射
- `/` - 主页面，显示统计信息和功能入口
- `/manage` - 账户管理页面，添加/删除账户
- `/fetch` - 取件操作页面
- `/fetched` - 已取件邮箱查看页面

#### 模板继承结构
- `layout.html` - 基础布局模板 (如果存在)
- 各功能页面模板继承基础布局
- 统一的CSS变量支持明暗主题切换

#### JavaScript功能模块
- **API通信**: 封装fetch请求处理
- **表单验证**: 客户端数据验证
- **动态更新**: 实时统计数据更新
- **用户交互**: 模态框、通知、加载状态

## 开发注意事项

### 数据一致性要求
- 所有账户数据修改必须通过 `cache_lock` 保护
- 文件I/O操作必须使用 `file_lock` 防止并发写入
- 缓存修改后需调用 `save_cache_to_file()` 持久化

### 错误处理模式
- 使用 `try-except` 包装所有文件操作
- 记录详细错误日志到 `data/app.log`
- 向用户返回友好的错误消息
- 文件损坏时自动重置为默认状态

### CSV导入兼容性
- 支持标准CSV格式和特殊分隔符格式
- 自动检测和跳过标题行
- 容错处理，单行错误不中断整体流程
- 提供详细的导入结果统计

### 性能优化策略
- 使用内存缓存减少文件I/O操作
- 批量操作优于单个操作
- 定时保存机制平衡性能和数据安全
- 大量数据使用分页显示

### API配置管理
- `API_BASE_URL` 可动态配置
- 支持本地和远程API服务器切换
- 配置变更实时生效
- 配置文件损坏自动恢复

## 测试和调试

### 日志系统
- 应用日志: `data/app.log`
- 日志级别: INFO (可通过环境变量调整)
- 关键操作都有详细日志记录

### 调试模式
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
python main.py
```

### 常见问题排查
1. **账户加载失败**: 检查 `accounts.json` 文件格式
2. **CSV导入错误**: 验证文件编码和分隔符格式
3. **数据不同步**: 确认缓存保存机制正常工作
4. **页面无法访问**: 检查端口5000是否被占用

## 部署配置

### 环境要求
- Python 3.7+
- Flask 2.3.3
- 操作系统: Windows/Linux/macOS

### 打包注意事项
- 确保 `templates/` 和 `data/` 目录被正确包含
- PyInstaller配置文件: `pyinstaller.spec`
- 打包后的data目录路径通过 `get_app_path()` 动态获取

### 生产部署
- 使用生产级WSGI服务器 (如Gunicorn)
- 配置反向代理 (如Nginx)
- 定期备份data目录中的JSON文件
- 监控日志文件大小和应用内存使用