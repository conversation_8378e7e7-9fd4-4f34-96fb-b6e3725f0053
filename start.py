#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单启动脚本
可以选择启动模式：完整模式（前端+后端）或仅前端模式
"""

import os
import sys
import subprocess
import threading
import time
import webbrowser
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = ['flask', 'fastapi', 'uvicorn', 'httpx', 'pydantic']
    missing = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        logger.error(f"缺少依赖包: {', '.join(missing)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    return True

def start_backend():
    """启动后端API服务器"""
    try:
        logger.info("正在启动FastAPI后端服务器 (端口8000)...")
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'
        
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "api_server:app", 
            "--host", "127.0.0.1", 
            "--port", "8000",
            "--log-level", "info"
        ], env=env)
        
        return process
    except Exception as e:
        logger.error(f"启动后端服务器失败: {e}")
        return None

def start_frontend():
    """启动前端Flask应用"""
    try:
        logger.info("正在启动Flask前端应用 (端口5000)...")
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'
        
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], env=env)
        
        return process
    except Exception as e:
        logger.error(f"启动前端应用失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 70)
    print("  Outlook邮件管理系统 - 集成版本")
    print("=" * 70)
    print("功能特点：")
    print("   • 本地账户管理 + 后端API邮件读取")
    print("   • 支持Outlook IMAP邮件访问")
    print("   • 批量账户导入和管理")
    print("   • 实时邮件浏览和搜索")
    print("-" * 70)
    print("启动模式选择：")
    print("   1. 完整模式 (推荐) - 前端 + 后端API服务")
    print("   2. 仅前端模式 - 只启动前端界面")
    print("   3. 仅后端模式 - 只启动API服务器")
    print("=" * 70)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 选择启动模式
    while True:
        try:
            choice = input("请选择启动模式 (1-3): ").strip()
            if choice in ['1', '2', '3']:
                break
            print("❌ 请输入有效选项 (1, 2, 或 3)")
        except KeyboardInterrupt:
            print("\n👋 退出程序")
            return
    
    # 确保data目录存在
    os.makedirs("data", exist_ok=True)
    
    processes = []
    
    try:
        if choice == '1':  # 完整模式
            print("\n🎯 启动完整模式...")
            
            # 启动后端
            backend_process = start_backend()
            if backend_process:
                processes.append(backend_process)
                logger.info("等待后端服务器启动...")
                time.sleep(4)
            
            # 启动前端
            frontend_process = start_frontend()
            if frontend_process:
                processes.append(frontend_process)
                logger.info("等待前端应用启动...")
                time.sleep(3)
            
            if processes:
                print("\n✅ 服务启动成功！")
                print("📱 前端界面: http://localhost:5000")
                print("🔧 后端API: http://localhost:8000")
                print("📚 API文档: http://localhost:8000/docs")
                print("\n🌐 正在打开浏览器...")
                webbrowser.open("http://localhost:5000")
            
        elif choice == '2':  # 仅前端模式
            print("\n🎯 启动仅前端模式...")
            frontend_process = start_frontend()
            if frontend_process:
                processes.append(frontend_process)
                time.sleep(3)
                print("\n✅ 前端应用启动成功！")
                print("📱 访问地址: http://localhost:5000")
                print("⚠️  注意：需要手动配置后端API地址")
                webbrowser.open("http://localhost:5000")
        
        elif choice == '3':  # 仅后端模式
            print("\n🎯 启动仅后端模式...")
            backend_process = start_backend()
            if backend_process:
                processes.append(backend_process)
                time.sleep(3)
                print("\n✅ 后端API服务启动成功！")
                print("🔧 API地址: http://localhost:8000")
                print("📚 API文档: http://localhost:8000/docs")
                webbrowser.open("http://localhost:8000/docs")
        
        if processes:
            print("\n📋 服务状态：")
            for i, process in enumerate(processes, 1):
                status = "运行中" if process.poll() is None else "已停止"
                print(f"   进程 {i}: {status}")
            
            print("\n⏹️  按 Ctrl+C 停止所有服务")
            
            # 等待进程
            for process in processes:
                process.wait()
    
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
    except Exception as e:
        logger.error(f"运行时错误: {e}")
    finally:
        # 清理进程
        for process in processes:
            if process.poll() is None:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    try:
                        process.kill()
                    except:
                        pass
        
        logger.info("🏁 所有服务已停止")

if __name__ == "__main__":
    main()