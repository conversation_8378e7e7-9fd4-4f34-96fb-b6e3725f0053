#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成启动脚本
同时启动Flask前端应用和FastAPI后端API服务器
"""

import subprocess
import threading
import time
import webbrowser
import logging
import os
import sys

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def start_flask_frontend():
    """启动Flask前端应用"""
    try:
        logger.info("启动Flask前端应用...")
        # 运行Flask应用
        subprocess.run([sys.executable, "main.py"], check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"Flask前端应用启动失败: {e}")
    except KeyboardInterrupt:
        logger.info("Flask前端应用被用户中断")

def start_fastapi_backend():
    """启动FastAPI后端API服务器"""
    try:
        logger.info("启动FastAPI后端API服务器...")
        # 运行FastAPI服务器
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "api_server:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--reload"
        ], check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"FastAPI后端服务器启动失败: {e}")
    except KeyboardInterrupt:
        logger.info("FastAPI后端服务器被用户中断")

def main():
    """主函数"""
    print("="*60)
    print("  Outlook邮件管理系统 - 集成版本")
    print("="*60)
    print("前端Flask应用: http://localhost:5000")
    print("后端API服务: http://localhost:8000")
    print("API文档: http://localhost:8000/docs")
    print("="*60)
    
    # 确保data目录存在
    os.makedirs("data", exist_ok=True)
    
    # 创建线程启动两个服务
    flask_thread = threading.Thread(target=start_flask_frontend, daemon=True)
    fastapi_thread = threading.Thread(target=start_fastapi_backend, daemon=True)
    
    try:
        # 启动FastAPI后端
        fastapi_thread.start()
        logger.info("FastAPI后端服务器启动中...")
        
        # 等待后端启动
        time.sleep(3)
        
        # 启动Flask前端
        logger.info("启动Flask前端应用...")
        flask_thread.start()
        
        # 等待前端启动
        time.sleep(2)
        
        # 打开浏览器
        logger.info("打开浏览器...")
        webbrowser.open("http://localhost:5000")
        
        # 等待线程完成
        flask_thread.join()
        fastapi_thread.join()
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在关闭服务...")
    except Exception as e:
        logger.error(f"启动服务时发生错误: {e}")

if __name__ == "__main__":
    main()