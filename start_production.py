#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境启动脚本
使用Gunicorn作为WSGI服务器，提供更好的性能和稳定性
"""

import os
import sys
import subprocess
import threading
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_production_dependencies():
    """检查生产环境依赖"""
    required_packages = ['gunicorn', 'gevent']
    missing = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        logger.error(f"缺少生产环境依赖: {', '.join(missing)}")
        logger.info("请运行: pip install gunicorn gevent")
        return False
    
    return True

def start_production_backend():
    """启动生产环境后端API服务器"""
    try:
        logger.info("启动生产环境FastAPI后端服务器...")
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'
        
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "api_server:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--workers", "4",  # 多进程
            "--worker-class", "uvicorn.workers.UvicornWorker",
            "--access-log",
            "--log-level", "info"
        ], env=env)
        
        return process
    except Exception as e:
        logger.error(f"启动后端服务器失败: {e}")
        return None

def start_production_frontend():
    """启动生产环境前端应用"""
    try:
        logger.info("启动生产环境Flask前端应用...")
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'
        env['FLASK_ENV'] = 'production'
        
        # 使用Gunicorn启动Flask应用
        process = subprocess.Popen([
            sys.executable, "-m", "gunicorn",
            "--bind", "0.0.0.0:5000",
            "--workers", "4",  # 4个工作进程
            "--worker-class", "gevent",  # 使用gevent异步工作
            "--worker-connections", "1000",
            "--timeout", "30",
            "--keepalive", "2",
            "--max-requests", "1000",
            "--max-requests-jitter", "100",
            "--access-logfile", "-",
            "--error-logfile", "-",
            "main:app"
        ], env=env)
        
        return process
    except Exception as e:
        logger.error(f"启动前端应用失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 70)
    print("  Outlook邮件管理系统 - 生产环境部署")
    print("=" * 70)
    print("特点:")
    print("   • Gunicorn WSGI服务器 (Flask)")
    print("   • Uvicorn ASGI服务器 (FastAPI)")
    print("   • 多进程工作模式")
    print("   • 异步处理优化")
    print("   • 生产级性能和稳定性")
    print("=" * 70)
    
    # 检查生产环境依赖
    if not check_production_dependencies():
        input("按回车键退出...")
        return
    
    # 确保data目录存在
    os.makedirs("data", exist_ok=True)
    
    processes = []
    
    try:
        print("\n启动生产环境服务...")
        
        # 启动后端
        backend_process = start_production_backend()
        if backend_process:
            processes.append(("FastAPI后端", backend_process))
            logger.info("等待后端服务器启动...")
            time.sleep(5)
        
        # 启动前端
        frontend_process = start_production_frontend()
        if frontend_process:
            processes.append(("Flask前端", frontend_process))
            logger.info("等待前端应用启动...")
            time.sleep(3)
        
        if processes:
            print("\n" + "=" * 70)
            print("生产环境服务启动成功!")
            print("访问地址:")
            print("  前端界面: http://localhost:5000")
            print("  后端API: http://localhost:8000")
            print("  API文档: http://localhost:8000/docs")
            print("\n性能特点:")
            print("  • Flask: 4个Gunicorn工作进程 + Gevent异步")
            print("  • FastAPI: 4个Uvicorn工作进程")
            print("  • 支持高并发访问")
            print("  • 生产级错误处理")
            print("=" * 70)
            
            logger.info("服务运行状态:")
            for name, process in processes:
                status = "运行中" if process.poll() is None else "已停止"
                logger.info(f"  {name}: {status}")
            
            print("\n按 Ctrl+C 停止所有服务")
            
            # 等待进程
            for name, process in processes:
                process.wait()
    
    except KeyboardInterrupt:
        print("\n\n正在停止生产环境服务...")
        logger.info("收到停止信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"运行时错误: {e}")
    finally:
        # 清理进程
        for name, process in processes:
            if process.poll() is None:
                try:
                    logger.info(f"停止 {name}...")
                    process.terminate()
                    process.wait(timeout=10)
                except:
                    try:
                        process.kill()
                    except:
                        pass
        
        logger.info("所有生产环境服务已停止")

if __name__ == "__main__":
    main()