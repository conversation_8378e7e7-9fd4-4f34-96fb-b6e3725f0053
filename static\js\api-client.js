/**
 * 后端API客户端
 * 用于与OutlookManager后端服务通信
 */
class APIClient {
    constructor(baseURL, authToken = null) {
        this.baseURL = baseURL.replace(/\/$/, ''); // 移除末尾斜杠
        this.authToken = authToken;
        this.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        
        if (this.authToken) {
            this.headers['Authorization'] = `Bearer ${this.authToken}`;
        }
    }

    /**
     * 设置认证令牌
     */
    setAuthToken(token) {
        this.authToken = token;
        if (token) {
            this.headers['Authorization'] = `Bearer ${token}`;
        } else {
            delete this.headers['Authorization'];
        }
    }

    /**
     * 通用请求方法
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: { ...this.headers, ...options.headers },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                const error = await response.text();
                throw new Error(`HTTP ${response.status}: ${error}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            return await response.text();
        } catch (error) {
            console.error(`API请求失败: ${endpoint}`, error);
            throw error;
        }
    }

    // ============================================================================
    // 认证相关API
    // ============================================================================

    /**
     * 获取认证配置信息
     */
    async getAuthConfig() {
        return await this.request('/auth/config');
    }

    // ============================================================================
    // 账户管理API
    // ============================================================================

    /**
     * 注册单个账户
     */
    async registerAccount(credentials) {
        return await this.request('/accounts', {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
    }

    /**
     * 批量注册账户
     */
    async registerMultipleAccounts(credentialsList) {
        return await this.request('/accounts', {
            method: 'POST',
            body: JSON.stringify(credentialsList)
        });
    }

    /**
     * 获取账户列表
     */
    async getAccounts(checkStatus = false) {
        return await this.request(`/accounts?check_status=${checkStatus}`);
    }

    /**
     * 批量验证账户
     */
    async verifyAccounts(accounts) {
        return await this.request('/accounts/verify', {
            method: 'POST',
            body: JSON.stringify({accounts})
        });
    }

    /**
     * 导入已验证的账户
     */
    async importVerifiedAccounts(credentials) {
        return await this.request('/accounts/import', {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
    }

    /**
     * 批量删除账户
     */
    async deleteAccounts(emails) {
        return await this.request('/accounts', {
            method: 'DELETE',
            body: JSON.stringify({emails})
        });
    }

    // ============================================================================
    // 邮件管理API
    // ============================================================================

    /**
     * 获取邮件列表
     */
    async getEmails(emailId, options = {}) {
        const {
            folder = 'all',
            page = 1,
            pageSize = 100,
            forceRefresh = false
        } = options;

        const params = new URLSearchParams({
            folder,
            page: page.toString(),
            page_size: pageSize.toString(),
            force_refresh: forceRefresh.toString()
        });

        return await this.request(`/emails/${encodeURIComponent(emailId)}?${params}`);
    }

    /**
     * 获取双栏视图邮件
     */
    async getDualViewEmails(emailId, options = {}) {
        const {
            inboxPage = 1,
            junkPage = 1,
            pageSize = 20,
            forceRefresh = false
        } = options;

        const params = new URLSearchParams({
            inbox_page: inboxPage.toString(),
            junk_page: junkPage.toString(),
            page_size: pageSize.toString(),
            force_refresh: forceRefresh.toString()
        });

        return await this.request(`/emails/${encodeURIComponent(emailId)}/dual-view?${params}`);
    }

    /**
     * 获取邮件详情
     */
    async getEmailDetail(emailId, messageId) {
        return await this.request(`/emails/${encodeURIComponent(emailId)}/${encodeURIComponent(messageId)}`);
    }

    // ============================================================================
    // 邮件操作API (扩展功能)
    // ============================================================================

    /**
     * 删除邮件 (如果后端支持)
     */
    async deleteEmail(emailId, messageId) {
        return await this.request(`/emails/${encodeURIComponent(emailId)}/${encodeURIComponent(messageId)}`, {
            method: 'DELETE'
        });
    }

    /**
     * 标记邮件为已读 (如果后端支持)
     */
    async markEmailAsRead(emailId, messageId) {
        return await this.request(`/emails/${encodeURIComponent(emailId)}/${encodeURIComponent(messageId)}/read`, {
            method: 'POST'
        });
    }

    /**
     * 移动邮件到指定文件夹 (如果后端支持)
     */
    async moveEmail(emailId, messageId, targetFolder) {
        return await this.request(`/emails/${encodeURIComponent(emailId)}/${encodeURIComponent(messageId)}/move`, {
            method: 'POST',
            body: JSON.stringify({ folder: targetFolder })
        });
    }

    // ============================================================================
    // API状态检查
    // ============================================================================

    /**
     * 检查API状态
     */
    async getApiStatus() {
        return await this.request('/api');
    }

    /**
     * 测试连接
     */
    async testConnection() {
        try {
            await this.getApiStatus();
            return true;
        } catch (error) {
            return false;
        }
    }
}

/**
 * 邮件客户端管理器
 * 整合本地存储和API调用
 */
class EmailClientManager {
    constructor() {
        this.apiClient = null;
        this.currentConfig = null;
        this.authToken = null;
    }

    /**
     * 初始化客户端
     */
    async initialize() {
        // 从本地配置获取API基础URL
        const config = await this.getLocalConfig();
        if (config && config.api_base_url) {
            this.apiClient = new APIClient(config.api_base_url);
            this.currentConfig = config;
        }
        return this.apiClient !== null;
    }

    /**
     * 设置认证信息
     */
    setAuth(token) {
        this.authToken = token;
        if (this.apiClient) {
            this.apiClient.setAuthToken(token);
        }
    }

    /**
     * 获取本地配置
     */
    async getLocalConfig() {
        try {
            const response = await fetch('/api/config');
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.error('获取本地配置失败:', error);
        }
        return null;
    }

    /**
     * 更新API配置
     */
    async updateApiConfig(apiBaseUrl) {
        try {
            const response = await fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    api_base_url: apiBaseUrl
                })
            });

            if (response.ok) {
                const result = await response.json();
                // 重新初始化API客户端
                this.apiClient = new APIClient(apiBaseUrl, this.authToken);
                this.currentConfig = {api_base_url: apiBaseUrl};
                return result;
            } else {
                throw new Error('配置更新失败');
            }
        } catch (error) {
            console.error('更新API配置失败:', error);
            throw error;
        }
    }

    /**
     * 测试API连接
     */
    async testApiConnection() {
        if (!this.apiClient) {
            throw new Error('API客户端未初始化');
        }
        return await this.apiClient.testConnection();
    }

    /**
     * 获取本地账户统计
     */
    async getLocalAccountStats() {
        try {
            const response = await fetch('/api/accounts/stats');
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.error('获取本地账户统计失败:', error);
        }
        return {total: 0, active: 0, fetched: 0};
    }

    /**
     * 同步账户到后端
     */
    async syncAccountsToBackend() {
        if (!this.apiClient || !this.authToken) {
            throw new Error('API客户端或认证信息未配置');
        }

        try {
            // 获取本地账户
            const localAccountsResponse = await fetch('/accounts');
            if (!localAccountsResponse.ok) {
                throw new Error('获取本地账户失败');
            }
            const localAccounts = await localAccountsResponse.json();

            // 转换为后端API格式
            const credentialsList = Object.keys(localAccounts).map(email => ({
                email: email,
                refresh_token: localAccounts[email].refresh_token,
                client_id: localAccounts[email].client_id
            }));

            if (credentialsList.length === 0) {
                return {success: true, message: '没有账户需要同步'};
            }

            // 批量验证账户
            const verificationResults = await this.apiClient.verifyAccounts(credentialsList);
            
            // 筛选验证成功的账户
            const validCredentials = verificationResults
                .filter(result => result.status === 'success')
                .map(result => result.credentials);

            if (validCredentials.length === 0) {
                return {success: false, message: '没有有效的账户可以同步'};
            }

            // 导入验证成功的账户
            const importResults = await this.apiClient.importVerifiedAccounts(validCredentials);
            
            return {
                success: true,
                message: `成功同步 ${validCredentials.length}/${credentialsList.length} 个账户`,
                details: {
                    total: credentialsList.length,
                    valid: validCredentials.length,
                    imported: importResults.length
                }
            };
        } catch (error) {
            console.error('同步账户到后端失败:', error);
            throw error;
        }
    }

    /**
     * 从后端获取邮件
     */
    async fetchEmailsFromBackend(emailId, options = {}) {
        if (!this.apiClient || !this.authToken) {
            throw new Error('API客户端或认证信息未配置');
        }

        return await this.apiClient.getEmails(emailId, options);
    }

    /**
     * 获取邮件详情
     */
    async getEmailDetailFromBackend(emailId, messageId) {
        if (!this.apiClient || !this.authToken) {
            throw new Error('API客户端或认证信息未配置');
        }

        return await this.apiClient.getEmailDetail(emailId, messageId);
    }
}

// 创建全局实例
window.emailClientManager = new EmailClientManager();