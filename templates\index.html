<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱 API 客户端</title>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ LOGO_URL }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ LOGO_URL }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ LOGO_URL }}">
    <link rel="manifest" href="/manifest.json">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#ffffff">
    <style>
        :root {
            --bg-color: #f8f9fa;
            --text-color: #212529;
            --accent-color: #0078d4; /* Outlook blue */
            --card-bg: #fff;
            --card-border: #c8c8c8;
            --button-primary: #0078d4; /* Outlook blue */
            --button-primary-hover: #005a9e;
            --button-secondary: #f3f3f3;
            --button-secondary-hover: #e0e0e0;
            --header-bg: #fff;
            --header-border-bottom: #e0e0e0;
            --form-label-color: #495057;
            --success-color: #28a745;
            --error-color: #dc3545;
            --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #121212; /* 更深的背景色 */
                --text-color: #f0f0f0;
                --accent-color: #3ab7f0;
                --card-bg: #1e1e1e; /* 更深的卡片背景 */
                --card-border: #444;
                --button-primary: #3ab7f0;
                --button-primary-hover: #2a88b8;
                --header-bg: #1e1e1e;
                --header-border-bottom: #444;
                --form-label-color: #bbb;
                --success-color: #4caf50;
                --error-color: #f44336;
                --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            padding-bottom: 50px;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .app-title {
            text-align: left;
        }
        
        .app-title h1, .app-title h2 {
            margin: 0;
        }
        
        .app-title h1 {
            font-size: 1.8rem;
            margin-bottom: 3px;
            color: var(--text-color);
        }
        
        .app-title h2 {
            font-size: 1.1rem;
            font-weight: 500;
            color: #0078d4;
        }
        
        .close-btn {
            background-color: #dc3545; /* 红色背景 */
            color: white;
            border: none;
            border-radius: 4px; /* 方形边角 */
            padding: 8px 16px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .close-btn:hover {
            background-color: #c82333; /* 深红色悬停效果 */
            transform: translateY(-2px);
        }
        
        .welcome-message {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .welcome-message h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }
        
        .welcome-message p {
            font-size: 1.1rem;
            color: var(--form-label-color);
            margin-top: 0;
        }

        h1 {
            color: var(--accent-color);
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
        }

        h2 {
            font-size: 1.5rem;
            color: var(--text-color);
            margin-top: 0;
            margin-bottom: 1rem;
            font-weight: 600;
            letter-spacing: -0.3px;
        }

        .box {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: var(--card-shadow);
            transition: all 0.2s ease;
            animation: fadeIn 0.3s ease-out forwards;
        }

        .box:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
        }

        input, textarea, select {
            width: 100%;
            padding: 10px 12px;
            margin-bottom: 1rem;
            border: 1px solid var(--card-border);
            border-radius: 6px;
            font-size: 0.95rem;
            background-color: var(--card-bg);
            color: var(--text-color);
            transition: all 0.2s ease;
            box-sizing: border-box;
        }

        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(58, 183, 240, 0.2);
        }

        button {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        button:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background-color: var(--button-primary);
            color: white;
            width: 100%;
        }

        .btn-primary:hover {
            background-color: var(--button-primary-hover);
        }

        .responsive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-button {
            background-color: var(--card-bg);
            color: var(--text-color);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 30px 20px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            transition: all 0.2s ease;
            text-decoration: none;
            box-shadow: var(--card-shadow);
        }

        .feature-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            border-color: var(--accent-color);
        }

        .feature-button i {
            font-size: 2.5rem;
            color: var(--accent-color);
            transition: all 0.2s ease;
        }

        .feature-button:hover i {
            transform: scale(1.1);
        }

        .feature-button span {
            display: block;
            color: var(--text-color);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .feature-button p {
            font-size: 0.9rem;
            color: var(--form-label-color);
            margin: 0;
        }

        .loading-spinner {
            display: none;
            margin: 1.5rem auto;
            text-align: center;
        }

        .loading-spinner .spinner {
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 3px solid var(--accent-color);
            width: 36px;
            height: 36px;
            animation: spin 0.8s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-box {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 15px;
            box-shadow: var(--card-shadow);
            text-align: center;
            transition: all 0.2s ease;
            border: 1px solid var(--card-border);
            animation: fadeIn 0.3s ease-out forwards;
        }

        .stat-box:nth-child(1) { animation-delay: 0.1s; }
        .stat-box:nth-child(2) { animation-delay: 0.15s; }
        .stat-box:nth-child(3) { animation-delay: 0.2s; }

        .stat-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--form-label-color);
        }

        /* 账号列表样式 */
        .accounts-list {
            margin-top: 20px;
        }
        
        .account-item {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--card-shadow);
            transition: all 0.2s ease;
            animation: fadeIn 0.3s ease-out forwards;
        }
        
        .account-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }
        
        .account-email {
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 12px;
            color: var(--accent-color);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .mail-count {
            background-color: var(--accent-color);
            color: white;
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 0.8rem;
            min-width: 24px;
            text-align: center;
        }
        
        .account-email:hover {
            text-decoration: underline;
        }
        
        .account-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .btn-mail {
            background-color: var(--button-primary);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .btn-mail i {
            font-size: 1.1rem;
        }
        
        .btn-secondary {
            background-color: var(--button-secondary);
            color: var(--text-color);
            border: 1px solid var(--card-border);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1.5rem;
            margin-top: 20px;
        }
        
        .no-accounts, .error-message {
            padding: 20px;
            text-align: center;
            color: var(--form-label-color);
        }
        
        .error-message {
            color: var(--error-color);
        }

        #fetched-accounts-section {
            display: none;
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            max-width: 1000px;
            background-color: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            z-index: 1000;
            max-height: 80vh;
            overflow-y: auto;
            padding: 20px;
        }
        
        #fetched-accounts-section.show {
            display: block;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            margin-bottom: 40px;
            gap: 25px;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            padding: 20px;
            text-align: center;
            flex: 1;
            transition: transform 0.3s ease-in-out;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
        }
        
        .stat-icon {
            font-size: 2rem;
            color: var(--accent-color);
            margin-bottom: 10px;
        }
        
        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--text-color);
            margin: 5px 0;
        }
        
        .stat-label {
            font-size: 1rem;
            color: #666;
            margin-bottom: 5px;
        }
        
        .main-buttons {
            display: flex;
            gap: 25px;
            margin-bottom: 40px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .main-button {
            flex: 1;
            min-width: 220px;
            max-width: 280px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: white;
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            padding: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: var(--text-color);
            border: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .main-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background-color: var(--accent-color);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .main-button:hover::before {
            opacity: 1;
        }
        
        .main-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }
        
        .main-button i {
            font-size: 3rem;
            color: var(--accent-color);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .main-button:hover i {
            transform: scale(1.1);
        }
        
        .main-button span {
            font-size: 1.3rem;
            font-weight: 500;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--card-border);
            padding-bottom: 15px;
        }

        .dialog-close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
            padding: 5px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .dialog-close-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        /* API 配置对话框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(3px);
        }
        
        .modal-content {
            background-color: var(--card-bg);
            margin: 8% auto;
            padding: 0;
            border: none;
            border-radius: 12px;
            width: 90%;
            max-width: 450px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            animation: modalFadeIn 0.25s ease-out forwards;
            overflow: hidden;
        }
        
        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 20px;
            background-color: var(--accent-color);
            color: white;
        }
        
        .modal-header h2 {
            margin: 0;
            font-size: 1.3rem;
            color: white;
            font-weight: 500;
        }
        
        .dialog-close {
            font-size: 1.5rem;
            font-weight: normal;
            cursor: pointer;
            color: white;
            opacity: 0.8;
            transition: opacity 0.2s ease;
            line-height: 1;
        }
        
        .dialog-close:hover {
            opacity: 1;
        }
        
        .modal-body {
            padding: 25px 20px;
        }
        
        .modal-body p {
            margin-top: 0;
            margin-bottom: 15px;
            color: var(--form-label-color);
        }
        
        .api-url-input {
            margin-top: 5px;
            font-size: 1rem;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .api-url-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }
        
        .modal-footer {
            padding: 0 20px 20px;
            text-align: right;
        }
        
        #save-api-config {
            background-color: var(--accent-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 24px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        #save-api-config:hover {
            background-color: var(--button-primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .small {
            font-size: 0.85rem;
        }
        
        .text-muted {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-top">
            <div class="app-title">
                <h1>邮箱 API 客户端</h1>
                <h2>账号管理工具</h2>
            </div>
            <button id="closeAppBtn" class="close-btn">
                <i class="ri-close-circle-line"></i> 关闭程序
            </button>
        </div>
        
        <div class="welcome-message">
            <h1>欢迎使用 Outlook API 客户端</h1>
            <p>请选择下方功能开始使用</p>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-icon"><i class="ri-user-line"></i></div>
                <div class="stat-label">账号数量</div>
                <div class="stat-value" id="total-count">0</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon"><i class="ri-shield-check-line"></i></div>
                <div class="stat-label">未取件邮箱</div>
                <div class="stat-value" id="active-count">0</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon"><i class="ri-mail-check-line"></i></div>
                <div class="stat-label">已取件邮箱</div>
                <div class="stat-value" id="fetched-count">0</div>
            </div>
        </div>

        <div class="main-buttons">
            <a href="/manage" class="main-button">
                <i class="ri-user-settings-line"></i>
                <span>账号管理</span>
            </a>
            <a href="/fetch" class="main-button">
                <i class="ri-mail-line"></i>
                <span>取件操作</span>
            </a>
            <a href="/fetched" class="main-button">
                <i class="ri-inbox-line"></i>
                <span>已取件邮箱</span>
            </a>
            <button class="main-button" id="api-config-btn">
                <i class="ri-settings-line"></i>
                <span>API 配置</span>
            </button>
        </div>
    </div>

    <!-- API 配置对话框 -->
    <div id="api-config-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>API 服务器配置</h2>
                <span class="dialog-close">&times;</span>
            </div>
            <div class="modal-body">
                <p>设置API服务器地址，格式: https://你的域名/api</p>
                <input type="text" id="api-url-input" placeholder="输入API服务器地址" class="api-url-input">
                <p class="text-muted small">不填写时将使用系统默认API地址</p>
            </div>
            <div class="modal-footer">
                <button id="save-api-config" class="btn-primary">保存配置</button>
            </div>
        </div>
    </div>

    <script>
        // 在页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            const apiBaseUrl = "{{ API_BASE_URL }}";
            
            // 加载账号统计信息
            loadAccountStats();
            
            // 设置心跳检测
            const heartbeatInterval = 10000; // 10秒一次心跳
            setInterval(() => {
                fetch('/heartbeat').catch(err => console.log('心跳检测失败'));
            }, heartbeatInterval);
            
            // API配置相关功能
            setupApiConfigModal();
            
            // 关闭程序按钮功能
            document.getElementById('closeAppBtn').addEventListener('click', function() {
                if (confirm('确定要关闭程序吗？')) {
                    fetch('/shutdown', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'shutting_down') {
                            alert('程序正在关闭，关闭完成请刷新页面');
                        }
                    })
                    .catch(error => {
                        console.error('关闭程序失败:', error);
                        alert('关闭程序失败，请尝试手动关闭窗口');
                    });
                }
            });
        });
        
        // API配置对话框功能
        function setupApiConfigModal() {
            const modal = document.getElementById('api-config-modal');
            const btn = document.getElementById('api-config-btn');
            const closeBtn = document.getElementsByClassName('dialog-close')[0];
            const saveBtn = document.getElementById('save-api-config');
            const urlInput = document.getElementById('api-url-input');
            
            // 获取当前API配置
            fetch('/api/config')
            .then(response => response.json())
            .then(data => {
                // 只有当API地址与默认值不同时才显示
                if (data.api_base_url && data.api_base_url !== "https://outlook.jry1.me/api") {
                    urlInput.value = data.api_base_url;
                } else {
                    urlInput.value = "";
                }
            })
            .catch(error => {
                console.error('获取API配置失败');
            });
            
            // 打开对话框
            btn.onclick = function() {
                modal.style.display = "block";
            }
            
            // 关闭对话框
            closeBtn.onclick = function() {
                modal.style.display = "none";
            }
            
            // 点击对话框外部关闭
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = "none";
                }
            }
            
            // 保存配置
            saveBtn.onclick = function() {
                const newUrl = document.getElementById('api-url-input').value.trim();
                
                // 如果输入为空，则恢复默认API地址
                const apiUrl = newUrl || "https://outlook.jry1.me/api";
                
                if (apiUrl && !apiUrl.startsWith('http')) {
                    alert('API URL必须以http或https开头');
                    return;
                }
                
                fetch('/api/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ api_base_url: apiUrl }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('API配置已更新');
                        modal.style.display = "none";
                    } else {
                        alert('更新失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('保存API配置失败:', error);
                    alert('保存失败，请检查网络连接');
                });
            }
        }
        
        // 加载账号统计信息
        function loadAccountStats() {
            fetch('/api/accounts/stats')
            .then(response => response.json())
            .then(data => {
                document.getElementById('total-count').textContent = data.total;
                document.getElementById('active-count').textContent = data.active;
                document.getElementById('fetched-count').textContent = data.fetched;
            })
            .catch(error => {
                console.error('获取账号统计失败:', error);
            });
        }
    </script>
</body>
</html> 