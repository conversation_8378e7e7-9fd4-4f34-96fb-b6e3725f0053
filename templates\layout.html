<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱 API 客户端</title>
    <link rel="icon" href="https://img.xwyue.com/i/2025/01/23/6791c8b24239a.png" type="image/png">
    <link rel="manifest" href="/manifest.json">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .logo {
            max-height: 40px;
            margin-right: 10px;
        }
        .footer {
            margin-top: auto;
            padding-top: 20px;
            padding-bottom: 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        .container {
            flex: 1;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .app-title {
            display: flex;
            align-items: center;
        }
        
        .logo {
            max-height: 40px;
            margin-right: 10px;
        }
        
        .close-btn {
            background-color: #dc3545; /* 红色背景 */
            color: white;
            border: none;
            border-radius: 4px; /* 方形边角 */
            padding: 8px 16px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .close-btn:hover {
            background-color: #c82333; /* 深红色悬停效果 */
            transform: translateY(-2px);
        }
        
        /* 暗黑模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #222;
                color: #f8f9fa;
            }
            .footer {
                background-color: #333;
                border-top: 1px solid #444;
            }
            .card, .navbar {
                background-color: #333 !important;
                color: #f8f9fa !important;
            }
            .card-body, .card-header, .card-footer {
                background-color: #333 !important;
                color: #f8f9fa !important;
            }
            .form-control, .form-select {
                background-color: #444;
                color: #f8f9fa;
                border-color: #555;
            }
            .table {
                color: #f8f9fa;
            }
            .table-striped tbody tr:nth-of-type(odd) {
                background-color: rgba(255, 255, 255, 0.05);
            }
            .modal-content {
                background-color: #333;
                color: #f8f9fa;
            }
            .btn-close {
                filter: invert(1) grayscale(100%) brightness(200%);
            }
        }
    </style>
    {% block styles %}{% endblock %}
</head>
<body>
    <div class="container">
        <div class="header-top">
            <div class="app-title">
                <img src="https://img.xwyue.com/i/2025/01/23/6791c8b24239a.png" alt="Logo" class="logo">
                <h1>邮箱 API 客户端</h1>
            </div>
            <button id="closeAppBtn" class="close-btn">
                <i class="bi bi-x-circle"></i> 关闭程序
            </button>
        </div>
        
        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-4">
        <div class="container text-center">
            <p class="mb-0">© 2025 邮箱 API 客户端 | 本地版</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}

    <!-- 心跳检测脚本 -->
    <script>
        // 心跳检测，每3秒发送一次
        function sendHeartbeat() {
            fetch('/heartbeat')
                .then(response => response.json())
                .catch(error => console.error('心跳检测错误:', error));
        }

        // 页面关闭前发送关闭信号
        window.addEventListener('beforeunload', function() {
            navigator.sendBeacon('/shutdown', '');
        });

        // 启动心跳检测
        setInterval(sendHeartbeat, 3000);
        sendHeartbeat(); // 立即发送一次
        
        // 关闭程序按钮功能
        document.getElementById('closeAppBtn').addEventListener('click', function() {
            if (confirm('确定要关闭程序吗？')) {
                fetch('/shutdown', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'shutting_down') {
                        alert('程序正在关闭，关闭完成请刷新页面');
                    }
                })
                .catch(error => {
                    console.error('关闭程序失败:', error);
                    alert('关闭程序失败，请尝试手动关闭窗口');
                });
            }
        });
    </script>
</body>
</html> 