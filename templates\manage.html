<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理账号</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@4.1.0/fonts/remixicon.css">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ LOGO_URL }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ LOGO_URL }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ LOGO_URL }}">
    <link rel="manifest" href="/manifest.json">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#ffffff">
    <style>
        :root {
            --bg-color: #f8f9fa;
            --text-color: #212529;
            --accent-color: #0078d4; /* Outlook blue */
            --card-bg: #fff;
            --card-border: #c8c8c8;
            --button-primary: #0078d4; /* Outlook blue */
            --button-primary-hover: #005a9e;
            --delete-button-bg: #dc3545;
            --delete-button-hover: #c82333;
            --header-bg: #fff;
            --header-border-bottom: #e0e0e0;
            --form-label-color: #495057;
            --success-color: #28a745;
            --error-color: #dc3545;
            --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #121212; /* 更深的背景色 */
                --text-color: #f0f0f0; 
                --accent-color: #3ab7f0; 
                --card-bg: #1e1e1e; /* 更深的卡片背景 */
                --card-border: #444;
                --button-primary: #3ab7f0;
                --button-primary-hover: #2a88b8;
                --delete-button-bg: #c82333;
                --delete-button-hover: #b01a28;
                --header-bg: #1e1e1e;
                --header-border-bottom: #444;
                --form-label-color: #bbb;
                --success-color: #4caf50;
                --error-color: #f44336;
                --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            padding: 30px 0 50px 0;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close-btn {
            background-color: #dc3545; /* 红色背景 */
            color: white;
            border: none;
            border-radius: 4px; /* 方形边角 */
            padding: 8px 16px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .close-btn:hover {
            background-color: #c82333; /* 深红色悬停效果 */
            transform: translateY(-2px);
        }

        h1 {
            color: var(--accent-color);
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
            letter-spacing: -0.5px;
        }

        /* 双面板布局 */
        .split-layout {
            display: flex;
            gap: 25px;
            margin-top: 20px;
        }

        .left-panel {
            flex: 0 0 450px;
            min-width: 350px;
            display: flex;
            flex-direction: column;
        }

        .right-panel {
            flex: 1;
            min-width: 500px;
            display: flex;
            flex-direction: column;
        }

        /* 保证左右面板的子元素垂直填充空间 */
        .left-panel > div:first-child,
        .right-panel > div:first-child {
            margin-top: 0;
        }
        
        .left-panel > div:last-child,
        .right-panel > div:last-child {
            margin-bottom: 0;
            flex-grow: 1;
        }

        /* 确保主要内容区域高度一致 */
        .accounts-section {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        #accounts {
            flex-grow: 1;
            min-height: 300px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .split-layout {
                flex-direction: column;
            }
            
            .left-panel, .right-panel {
                flex: 1;
                width: 100%;
                min-width: unset;
            }
        }

        h2 {
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        h2 i {
            color: var(--accent-color);
            font-size: 1.3rem;
        }

        .back-link {
            display: flex;
            align-items: center;
        }

        .back-link a {
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: var(--accent-color);
            font-weight: 500;
            transition: transform 0.2s ease;
        }

        .back-link a:hover {
            transform: translateX(-3px);
        }

        .box {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: var(--card-shadow);
            transition: all 0.2s ease;
            animation: fadeIn 0.3s ease-out forwards;
        }

        .add-account-form, .csv-import-area {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .add-account-form:hover, .csv-import-area:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .add-account-form::before, .csv-import-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background-color: var(--accent-color);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .add-account-form:hover::before, .csv-import-area:hover::before {
            opacity: 1;
        }

        form {
            display: flex;
            flex-direction: column;
        }

        label {
            color: var(--form-label-color);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        input {
            padding: 12px;
            margin-bottom: 1rem;
            border: 1px solid var(--card-border);
            border-radius: 6px;
            background-color: var(--card-bg);
            color: var(--text-color);
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        button {
            background-color: var(--button-primary);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        button:hover {
            background-color: var(--button-primary-hover);
            transform: translateY(-1px);
        }

        .accounts-section {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 25px;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .accounts-section:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .accounts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--card-border);
        }

        .accounts-header h2 {
            margin: 0;
        }

        .batch-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .batch-actions label {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }

        .batch-actions input[type="checkbox"] {
            margin: 0;
            cursor: pointer;
        }

        .account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .account:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .account-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .account-info h2 {
            margin: 0;
            font-size: 1.1rem;
        }

        .account-checkbox {
            width: auto;
            margin-bottom: 0;
        }

        .actions {
            display: flex;
            gap: 10px;
        }

        .btn-danger {
            background-color: var(--delete-button-bg);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-danger:hover {
            background-color: var(--delete-button-hover);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: #f3f3f3;
            color: var(--text-color);
            border: 1px solid var(--card-border);
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 5px 12px;
            margin: 0 4px;
        }

        .page-info-text {
            margin-top: 8px;
            font-size: 0.85rem;
            color: var(--form-label-color);
            text-align: center;
            font-style: italic;
        }

        .loading-spinner {
            display: none;
            margin: 1.5rem auto;
            text-align: center;
        }

        .loading-spinner .spinner {
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 3px solid var(--accent-color);
            width: 36px;
            height: 36px;
            animation: spin 0.8s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .import-area {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: var(--card-shadow);
        }

        .import-area label {
            display: block;
            margin-bottom: 5px;
        }

        .import-area input {
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 10px;
        }

        .import-area textarea {
            width: 100%;
            padding: 12px;
            margin-bottom: 10px;
            border: 1px solid var(--card-border);
            border-radius: 4px;
            background-color: var(--card-bg);
            color: var(--text-color);
            font-size: 1rem;
            resize: vertical;
            box-sizing: border-box;
        }

        .test-connection-btn {
            background-color: #17a2b8;
            color: white;
        }

        .test-connection-btn:hover {
            background-color: #138496;
        }

        /* CSV导入区域样式 */
        .csv-import-area {
            background-color: var(--card-bg);
            border: 1px solid var(--card-border);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: var(--card-shadow);
        }

        /* 文件上传按钮样式 */
        .csv-form {
            margin-top: 15px;
        }

        .csv-form-row {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }

        .file-upload-text {
            padding: 12px;
            border: 1px solid var(--card-border);
            border-radius: 4px;
            background-color: var(--bg-color);
            color: var(--text-color);
            width: 100%;
            text-align: center;
            cursor: pointer;
            user-select: none;
        }

        .delimiter-select {
            padding: 12px;
            border: 1px solid var(--card-border);
            border-radius: 4px;
            background-color: var(--card-bg);
            color: var(--text-color);
            cursor: pointer;
            min-width: 120px;
        }

        .csv-upload-btn {
            flex: 1;
            padding: 12px;
            border-radius: 4px;
            background-color: var(--button-primary);
            color: white;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .csv-upload-btn:hover {
            background-color: var(--button-primary-hover);
        }

        .csv-template-link {
            color: var(--accent-color);
            text-decoration: none;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
        }

        .csv-template-link:hover {
            text-decoration: underline;
        }

        .csv-result {
            margin-top: 1.2rem;
            display: none;
            padding: 16px;
            border-radius: 8px;
            background-color: rgba(0, 0, 0, 0.03);
        }

        .csv-result-success {
            color: var(--success-color);
            font-weight: 600;
        }

        .csv-result-error {
            color: var(--error-color);
            font-weight: 600;
        }

        .csv-error-details {
            margin-top: 1rem;
            max-height: 200px;
            overflow-y: auto;
            padding: 12px;
            background-color: rgba(220, 53, 69, 0.1);
            border-radius: 6px;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* 文件上传改进 */
        .file-upload-wrapper {
            position: relative;
            width: 100%;
            height: 40px;
            overflow: hidden;
        }

        .file-upload-input {
            position: absolute;
            top: 0;
            right: 0;
            margin: 0;
            padding: 0;
            font-size: 20px;
            cursor: pointer;
            opacity: 0;
            height: 100%;
            width: 100%;
            z-index: 2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-top">
            <h1>管理账号</h1>
            <button id="closeAppBtn" class="close-btn">
                <i class="ri-close-circle-line"></i> 关闭程序
            </button>
        </div>
        
        <div class="back-link">
            <a href="/">
                <i class="ri-arrow-left-line"></i> 返回主页
            </a>
        </div>
        
        <div class="split-layout">
            <!-- 左侧面板：添加账号和导入功能 -->
            <div class="left-panel">
                <!-- CSV导入表单 -->
                <div class="csv-import-area">
                    <h2><i class="ri-file-upload-line"></i> 从CSV文件导入账号</h2>
                    <p>上传CSV文件批量导入账号。CSV格式：每行一个账号，包含Email、Client ID和Refresh Token三列。</p>
                    <div class="csv-form">
                        <div class="csv-form-row">
                            <div class="file-upload-wrapper">
                                <input type="file" id="csvFile" class="file-upload-input" accept=".csv">
                                <div class="file-upload-text">选择CSV文件</div>
                            </div>
                            <select id="csvDelimiter" class="delimiter-select">
                                <option value=",">逗号 (,)</option>
                                <option value=";">分号 (;)</option>
                                <option value="tab">制表符 (Tab)</option>
                                <option value="four-dashes" selected>四个短线 (----)</option>
                                <option value="auto">自动检测</option>
                            </select>
                        </div>
                        <div class="csv-form-row">
                            <button type="button" onclick="uploadCSV()" class="csv-upload-btn">导入CSV</button>
                            <a href="/csv-template" class="csv-template-link">下载CSV模板</a>
                        </div>
                        <div id="csvResult" class="csv-result">
                            <div id="csvResultMessage"></div>
                            <div id="csvErrorDetails" class="csv-error-details"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 文本直接导入 -->
                <div class="csv-import-area">
                    <h2><i class="ri-text-spacing"></i> 直接粘贴文本导入账号</h2>
                    <p>直接粘贴文本内容导入账号。格式：email----password----client_id----refresh_token</p>
                    <div class="csv-form">
                        <textarea id="importTextArea" rows="5" placeholder="粘贴账号文本，每行一个账号" style="width: 100%; padding: 10px; border-radius: 4px; border: 1px solid var(--card-border); background-color: var(--card-bg); color: var(--text-color);"></textarea>
                        <div class="csv-form-row" style="margin-top: 10px;">
                            <button type="button" onclick="importDirectText()" class="csv-upload-btn">导入文本</button>
                        </div>
                        <div id="textImportResult" class="csv-result">
                            <div id="textImportMessage"></div>
                            <div id="textImportErrors" class="csv-error-details"></div>
                        </div>
                    </div>
                </div>
                
                <div class="import-area">
                    <h2><i class="ri-file-add-line"></i> 账号导入</h2>
                    <label for="delimiter">分隔符:</label>
                    <input type="text" id="delimiter" placeholder="默认为 ----">
                    <textarea id="importText" placeholder="粘贴导入字符串，每行一个账号，例如：email----password----clientId----refreshToken&#10;支持批量导入，每个账号占一行" rows="6"></textarea>
                    <button onclick="importAccount()" class="btn-primary">导入账号</button>
                </div>
                
                <div class="add-account-form">
                    <h2><i class="ri-user-add-line"></i> 添加账号</h2>
                    <form id="addAccountForm">
                        <label for="email">邮箱:</label>
                        <input type="email" id="email" name="email" required>
                        <label for="refresh_token">Refresh Token:</label>
                        <input type="text" id="refresh_token" name="refresh_token" required>
                        <label for="client_id">Client ID:</label>
                        <input type="text" id="client_id" name="client_id" required>
                        <div style="display: flex; gap: 10px; margin-top: 10px;">
                            <button type="submit" style="flex: 3;">添加账号</button>
                            <button type="button" onclick="testConnection()" class="test-connection-btn" style="flex: 1;">测试</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 右侧面板：账号列表 -->
            <div class="right-panel">
                <div class="accounts-section">
                    <div class="accounts-header">
                        <h2><i class="ri-user-3-line"></i> 账号列表</h2>
                        <div class="batch-actions">
                            <label for="selectAll">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()"> 全选
                            </label>
                            <button id="batchDeleteBtn" onclick="batchDelete()" class="btn-danger" disabled>批量删除</button>
                            <button id="deleteAllBtn" onclick="deleteAllAccounts()" class="btn-danger">删除全部</button>
                        </div>
                    </div>
                    <div id="accounts"></div>
                    <div class="loading-spinner" id="loadingSpinner">
                        <div class="spinner"></div>
                        <p>加载中...</p>
                    </div>
                    <div class="pagination">
                        <button id="prevPage" class="btn-secondary" onclick="changePage(-1)">上一页</button>
                        <span id="pageInfo">第 1 页 / 共 10 页</span>
                        <button id="nextPage" class="btn-secondary" onclick="changePage(1)">下一页</button>
                    </div>
                    <div class="page-info-text">每页显示 15 个账号</div>
                </div>
            </div>
        </div>
    </div>
    <script>
        const ACCOUNTS_PER_PAGE = 15;
        let currentPage = 1;
        let totalAccounts = {};
        let totalPages = 1;
        let selectedAccounts = new Set();

        // 检查批量删除按钮状态
        function checkBatchDeleteButton() {
            document.getElementById('batchDeleteBtn').disabled = selectedAccounts.size === 0;
        }

        // 切换全选
        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('.account-checkbox');
            const selectAllCheckbox = document.getElementById('selectAll');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                const email = checkbox.getAttribute('data-email');
                
                if (selectAllCheckbox.checked) {
                    selectedAccounts.add(email);
                } else {
                    selectedAccounts.delete(email);
                }
            });
            
            checkBatchDeleteButton();
        }

        // 切换单个账号选择
        function toggleAccountSelection(email) {
            if (selectedAccounts.has(email)) {
                selectedAccounts.delete(email);
            } else {
                selectedAccounts.add(email);
            }
            
            // 检查是否全部选中，更新全选框状态
            const checkboxes = document.querySelectorAll('.account-checkbox');
            const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
            document.getElementById('selectAll').checked = allChecked;
            
            checkBatchDeleteButton();
        }

        // 批量删除所选账号
        async function batchDelete() {
            if (selectedAccounts.size === 0) {
                return;
            }
            
            if (!confirm(`确定要删除选中的 ${selectedAccounts.size} 个账号吗？`)) {
                return;
            }
            
            try {
                const emails = Array.from(selectedAccounts).join(',');
                const response = await fetch(`/manage?emails=${emails}`, { method: 'DELETE' });
                
                if (response.ok) {
                    const result = await response.text();
                    alert(result);
                    selectedAccounts.clear();
                    loadAccounts(currentPage);
                } else {
                    const error = await response.text();
                    alert('错误: ' + error);
                }
            } catch (error) {
                alert('错误: ' + error.message);
            }
        }

        // 更改页码
        function changePage(delta) {
            const newPage = currentPage + delta;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                loadAccounts(currentPage);
            }
        }

        // 显示加载动画
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'block';
        }

        // 隐藏加载动画
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }

        // 加载账号
        async function loadAccounts(page = 1) {
            showLoading();
            selectedAccounts.clear();
            document.getElementById('selectAll').checked = false;
            checkBatchDeleteButton();
            
            try {
                const response = await fetch('/accounts');
                totalAccounts = await response.json();
                const accountEntries = Object.entries(totalAccounts);
                totalPages = Math.ceil(accountEntries.length / ACCOUNTS_PER_PAGE);
                
                // 更新分页信息
                document.getElementById('prevPage').disabled = page <= 1;
                document.getElementById('nextPage').disabled = page >= totalPages;
                document.getElementById('pageInfo').textContent = `第 ${page} 页 / 共 ${totalPages || 1} 页`;
                
                // 获取当前页的账号
                const startIndex = (page - 1) * ACCOUNTS_PER_PAGE;
                const endIndex = startIndex + ACCOUNTS_PER_PAGE;
                const pageAccounts = accountEntries.slice(startIndex, endIndex);
                
                const accountsDiv = document.getElementById('accounts');
                accountsDiv.innerHTML = '';
                
                for (const [email, account] of pageAccounts) {
                    const accountDiv = document.createElement('div');
                    accountDiv.classList.add('account');
                    accountDiv.innerHTML = `
                        <div class="account-info">
                            <input type="checkbox" class="account-checkbox" data-email="${email}" onchange="toggleAccountSelection('${email}')">
                            <h2>${email}</h2>
                        </div>
                        <div class="actions">
                            <button class="btn-danger" onclick="deleteAccount(event, '${email}')">删除</button>
                        </div>
                    `;
                    accountsDiv.appendChild(accountDiv);
                }
                
                if (accountEntries.length === 0) {
                    accountsDiv.innerHTML = '<p>暂无账号，请添加或导入账号</p>';
                }
            } catch (error) {
                console.error('加载账号失败:', error);
                alert('加载账号失败，请刷新页面重试');
            } finally {
                hideLoading();
            }
        }
        
        async function deleteAccount(event, email) {
            event.preventDefault();
            if (!confirm('确定要删除账号 ' + email + ' 吗？')) {
                return;
            }

            try {
                const response = await fetch('/manage?email=' + email, { method: 'DELETE' });
                if (response.ok) {
                    alert('账号已删除');
                    loadAccounts(currentPage);
                } else {
                    const error = await response.text();
                    alert('错误: ' + error);
                }
            } catch (error) {
                alert('错误: ' + error.message);
            }
        }

        document.getElementById('addAccountForm').addEventListener('submit', async (event) => {
            event.preventDefault();
            showLoading();
            
            try {
                const formData = new FormData(event.target);
                const email = formData.get('email').trim();
                const refresh_token = formData.get('refresh_token').trim();
                const client_id = formData.get('client_id').trim();
                
                // 简单验证表单数据
                if (!email || !refresh_token || !client_id) {
                    alert('请填写所有必填字段');
                    return;
                }

                const response = await fetch('/manage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        refresh_token: refresh_token,
                        client_id: client_id
                    })
                });

                if (response.ok) {
                    alert('账号添加成功');
                    loadAccounts(currentPage);
                    event.target.reset();
                } else {
                    const error = await response.text();
                    alert('错误: ' + error);
                }
            } catch (error) {
                alert('错误: ' + error.message);
            } finally {
                hideLoading();
            }
        });

        // 使用 Worker 线程进行并行导入
        function createWorker(delimiter) {
            const workerCode = `
                self.onmessage = function(e) {
                    const { line, index, delimiter } = e.data;
                    const parts = line.split(delimiter);
                    
                    if (parts.length >= 4) {
                        const email = parts[0].trim();
                        const clientId = parts[2].trim();
                        const refreshToken = parts[3].trim();
                        
                        self.postMessage({
                            success: true,
                            data: { email, clientId, refreshToken, index }
                        });
                    } else {
                        self.postMessage({
                            success: false,
                            error: '格式错误',
                            line: line,
                            index: index
                        });
                    }
                };
            `;
            
            const blob = new Blob([workerCode], { type: 'application/javascript' });
            const url = URL.createObjectURL(blob);
            return new Worker(url);
        }

        function importAccount() {
            const importText = document.getElementById('importText').value;
            const delimiter = document.getElementById('delimiter').value || '----';
            
            // 分割每一行作为单独的账号
            const lines = importText.trim().split('\n').filter(line => line.trim() !== '');
            
            if (lines.length === 0) {
                alert('请输入有效的账号信息');
                return;
            }
            
            if (lines.length === 1) {
                // 单个账号导入
                processSingleAccount(lines[0], delimiter);
            } else {
                // 批量导入
                processMultipleAccounts(lines, delimiter);
            }
        }
        
        function processSingleAccount(accountStr, delimiter) {
            const parts = accountStr.split(delimiter);
            
            if (parts.length >= 4) {
                const email = parts[0];
                const clientId = parts[2];
                const refreshToken = parts[3];

                document.getElementById('email').value = email;
                document.getElementById('client_id').value = clientId;
                document.getElementById('refresh_token').value = refreshToken;
                alert('账号信息已填充到表单，请点击"添加账号"按钮提交。');
            } else {
                alert('导入格式不正确，请检查示例格式和分隔符设置。');
            }
        }
        
        async function processMultipleAccounts(accountLines, delimiter) {
            // 显示进度
            showLoading();
            
            let successCount = 0;
            let failCount = 0;
            let errors = [];
            const totalAccounts = accountLines.length;
            
            // 创建导入状态元素
            const statusDiv = document.createElement('div');
            statusDiv.style.marginTop = '10px';
            statusDiv.style.padding = '10px';
            statusDiv.style.backgroundColor = 'var(--card-bg)';
            statusDiv.style.border = '1px solid var(--card-border)';
            statusDiv.style.borderRadius = '0.3rem';
            statusDiv.innerHTML = `<p>正在导入 ${totalAccounts} 个账号...</p>`;
            document.querySelector('.import-area').appendChild(statusDiv);
            
            // 使用并行导入方法
            // 首先解析所有数据
            const parsedData = [];
            const maxWorkers = Math.min(navigator.hardwareConcurrency || 4, 8); // 最多使用8个线程
            const workers = [];
            const workerResults = new Array(accountLines.length);
            let completedWorkers = 0;
            
            // 创建Worker池
            for (let i = 0; i < maxWorkers; i++) {
                const worker = createWorker(delimiter);
                workers.push(worker);
                
                worker.onmessage = function(e) {
                    workerResults[e.data.index || e.data.data.index] = e.data;
                    completedWorkers++;
                    
                    // 更新状态
                    statusDiv.innerHTML = `<p>正在解析账号信息... (${completedWorkers}/${accountLines.length})</p>`;
                    
                    // 检查是否所有Worker都完成了
                    if (completedWorkers === accountLines.length) {
                        // 所有解析完成，关闭Worker
                        workers.forEach(w => w.terminate());
                        
                        // 处理解析结果
                        let validAccounts = [];
                        for (const result of workerResults) {
                            if (result.success) {
                                validAccounts.push(result.data);
                            } else {
                                failCount++;
                                errors.push(`#${result.index + 1} ${result.line}: ${result.error}`);
                            }
                        }
                        
                        // 开始实际的API导入
                        importParsedAccounts(validAccounts, statusDiv);
                    }
                };
            }
            
            // 分配任务给Worker
            for (let i = 0; i < accountLines.length; i++) {
                const workerIndex = i % maxWorkers;
                workers[workerIndex].postMessage({
                    line: accountLines[i],
                    index: i,
                    delimiter: delimiter
                });
            }
        }
        
        // 导入已解析的账号数据
        async function importParsedAccounts(accounts, statusDiv) {
            let successCount = 0;
            let failCount = 0;
            let errors = [];
            const totalValid = accounts.length;
            
            // 可以一次最多并行多少请求
            const MAX_PARALLEL_REQUESTS = 5;
            
            // 更新状态
            statusDiv.innerHTML = `<p>开始导入 ${totalValid} 个有效账号...</p>`;
            
            // 使用异步队列处理
            const processQueue = async () => {
                for (let i = 0; i < accounts.length; i += MAX_PARALLEL_REQUESTS) {
                    const batch = accounts.slice(i, i + MAX_PARALLEL_REQUESTS);
                    const batchPromises = batch.map(async (account) => {
                        try {
                            const response = await fetch('/manage', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    email: account.email,
                                    refresh_token: account.refreshToken,
                                    client_id: account.clientId
                                })
                            });
                            
                            if (response.ok) {
                                return { success: true, email: account.email, index: account.index };
                            } else {
                                const error = await response.text();
                                return { success: false, email: account.email, error, index: account.index };
                            }
                        } catch (error) {
                            return { success: false, email: account.email, error: error.message, index: account.index };
                        }
                    });
                    
                    // 等待本批次全部完成
                    const batchResults = await Promise.all(batchPromises);
                    
                    // 处理结果
                    for (const result of batchResults) {
                        if (result.success) {
                            successCount++;
                        } else {
                            failCount++;
                            errors.push(`#${result.index + 1} ${result.email}: ${result.error}`);
                        }
                    }
                    
                    // 更新状态
                    statusDiv.innerHTML = `<p>导入进度: ${successCount + failCount}/${totalValid}</p>`;
                }
            };
            
            // 执行导入
            await processQueue();
            
            // 所有导入完成
            hideLoading();
            if (successCount > 0) {
                loadAccounts(currentPage); // 重新加载账号列表
            }
            
            // 移除状态元素
            document.querySelector('.import-area').removeChild(statusDiv);
            
            // 显示结果
            let message = `导入完成：共 ${successCount + failCount} 个账号\n`;
            message += `✅ 成功: ${successCount} 个\n`;
            if (failCount > 0) {
                message += `❌ 失败: ${failCount} 个\n\n`;
                message += errors.join('\n');
            }
            
            alert(message);
        }

        // 删除全部账号
        async function deleteAllAccounts() {
            const accountCount = Object.keys(totalAccounts).length;
            
            if (accountCount === 0) {
                alert('当前没有账号可删除');
                return;
            }
            
            if (!confirm(`确定要删除全部 ${accountCount} 个账号吗？此操作不可恢复！`)) {
                return;
            }
            
            try {
                showLoading();
                const response = await fetch('/manage?delete_all=true', { method: 'DELETE' });
                
                if (response.ok) {
                    const result = await response.text();
                    alert(result);
                    selectedAccounts.clear();
                    loadAccounts(1); // 重置回第一页
                } else {
                    const error = await response.text();
                    alert('错误: ' + error);
                }
            } catch (error) {
                alert('错误: ' + error.message);
            } finally {
                hideLoading();
            }
        }
        
        // 测试账号连接
        async function testConnection() {
            const email = document.getElementById('email').value.trim();
            const refresh_token = document.getElementById('refresh_token').value.trim();
            const client_id = document.getElementById('client_id').value.trim();
            
            if (!email || !refresh_token || !client_id) {
                alert('请填写完整的账号信息后再测试');
                return;
            }
            
            try {
                showLoading();
                const response = await fetch('/test-connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        refresh_token: refresh_token,
                        client_id: client_id
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ ' + result.message);
                } else {
                    alert('❌ ' + result.message);
                }
            } catch (error) {
                alert('测试连接失败: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        // 上传CSV文件
        async function uploadCSV() {
            const fileInput = document.getElementById('csvFile');
            const delimiterSelect = document.getElementById('csvDelimiter');
            const resultDiv = document.getElementById('csvResult');
            const resultMessage = document.getElementById('csvResultMessage');
            const errorDetails = document.getElementById('csvErrorDetails');
            
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('请选择CSV文件');
                return;
            }
            
            const file = fileInput.files[0];
            if (!file.name.endsWith('.csv')) {
                alert('只支持CSV文件格式');
                return;
            }
            
            const formData = new FormData();
            formData.append('csv_file', file);
            formData.append('delimiter', delimiterSelect.value);
            
            try {
                showLoading();
                resultDiv.style.display = 'none';
                
                const response = await fetch('/upload-csv', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultMessage.innerHTML = `
                        <div class="csv-result-success">
                            ${result.message}
                        </div>
                    `;
                    
                    // 清空错误详情
                    errorDetails.innerHTML = '';
                    
                    // 如果有错误详情，显示
                    if (result.result && result.result.errors && result.result.errors.length > 0) {
                        errorDetails.style.display = 'block';
                        errorDetails.innerHTML = result.result.errors.join('<br>');
                    } else {
                        errorDetails.style.display = 'none';
                    }
                    
                    // 刷新账号列表
                    loadAccounts(currentPage);
                } else {
                    resultMessage.innerHTML = `
                        <div class="csv-result-error">
                            导入失败: ${result.message}
                        </div>
                    `;
                    errorDetails.style.display = 'none';
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                resultMessage.innerHTML = `
                    <div class="csv-result-error">
                        导入过程中出错: ${error.message}
                    </div>
                `;
                resultDiv.style.display = 'block';
                errorDetails.style.display = 'none';
            } finally {
                hideLoading();
                // 清空文件输入框，以便再次上传同一文件
                fileInput.value = '';
            }
        }

        // 导入直接粘贴的文本
        async function importDirectText() {
            const textArea = document.getElementById('importTextArea');
            const resultDiv = document.getElementById('textImportResult');
            const resultMessage = document.getElementById('textImportMessage');
            const errorDetails = document.getElementById('textImportErrors');
            
            const text = textArea.value.trim();
            if (!text) {
                alert('请粘贴账号文本');
                return;
            }
            
            try {
                showLoading();
                resultDiv.style.display = 'none';
                
                const response = await fetch('/import-text', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text,
                        delimiter: '----'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultMessage.innerHTML = `
                        <div class="csv-result-success">
                            ${result.message}
                        </div>
                    `;
                    
                    // 清空错误详情
                    errorDetails.innerHTML = '';
                    
                    // 如果有错误详情，显示
                    if (result.result && result.result.errors && result.result.errors.length > 0) {
                        errorDetails.style.display = 'block';
                        errorDetails.innerHTML = result.result.errors.join('<br>');
                    } else {
                        errorDetails.style.display = 'none';
                    }
                    
                    // 清空文本区域
                    textArea.value = '';
                    
                    // 刷新账号列表
                    loadAccounts(currentPage);
                } else {
                    resultMessage.innerHTML = `
                        <div class="csv-result-error">
                            导入失败: ${result.message}
                        </div>
                    `;
                    errorDetails.style.display = 'none';
                }
                
                resultDiv.style.display = 'block';
            } catch (error) {
                resultMessage.innerHTML = `
                    <div class="csv-result-error">
                        导入过程中出错: ${error.message}
                    </div>
                `;
                resultDiv.style.display = 'block';
                errorDetails.style.display = 'none';
            } finally {
                hideLoading();
            }
        }

        // 更新文件上传视觉反馈
        document.querySelector('.file-upload-input').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : '选择CSV文件';
            document.querySelector('.file-upload-text').textContent = fileName;
        });

        // 初始化
        loadAccounts(1);

        document.addEventListener('DOMContentLoaded', function() {
            // 加载账号列表
            loadAccountsWithPagination();
            
            // 分页按钮事件绑定
            document.getElementById('prevPageBtn').addEventListener('click', function() {
                changePage(-1);
            });
            
            document.getElementById('nextPageBtn').addEventListener('click', function() {
                changePage(1);
            });
            
            // 关闭程序按钮功能
            document.getElementById('closeAppBtn').addEventListener('click', function() {
                if (confirm('确定要关闭程序吗？')) {
                    fetch('/shutdown', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'shutting_down') {
                            alert('程序正在关闭，关闭完成请刷新页面');
                        }
                    })
                    .catch(error => {
                        console.error('关闭程序失败:', error);
                        alert('关闭程序失败，请尝试手动关闭窗口');
                    });
                }
            });
            
            // 添加账号表单提交
            // ... existing code ...
        });
    </script>
</body>
</html> 