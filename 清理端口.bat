@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1

:: 定义需要清理的端口列表
set "ports=5000 8000"

for %%p in (%ports%) do (
    echo 正在精确查找占用 %%p 端口的进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr /r /c:":%%p[ ]"') do (
        echo 找到进程 PID: %%a
        taskkill /pid %%a /f >nul 2>&1
        if !errorlevel! equ 0 (
            echo 成功终止进程 %%a
        ) else (
            echo 终止进程 %%a 失败！请以管理员身份运行
        )
    )
    echo ----------------------
)
echo 所有端口清理完成
pause